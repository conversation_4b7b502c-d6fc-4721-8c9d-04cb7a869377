import { Locale } from '@/lib/i18n';

// 博客文章数据类型定义
export interface BlogPost {
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  date: string;
  tags: string[];
  category: string;
  readTime: string;
  author: string;
  featured: boolean;
  coverImage?: string;
}

// 多语言博客文章数据
export interface LocalizedBlogPost {
  zh: BlogPost;
  en: BlogPost;
}

// 博客文章数据
export const blogPosts: LocalizedBlogPost[] = [
  {
    zh: {
      slug: 'swiftui-5-new-features',
      title: 'SwiftUI 5.0 新特性完全指南',
      excerpt: '深入探索 SwiftUI 5.0 的革命性新特性，包括新的动画系统、改进的数据绑定、增强的导航功能，以及如何在实际项目中应用这些特性。',
      content: `
# SwiftUI 5.0 新特性完全指南

SwiftUI 5.0 带来了许多令人兴奋的新特性，这些特性将彻底改变我们构建 iOS 应用的方式。在这篇文章中，我们将深入探讨这些新特性，并学习如何在实际项目中应用它们。

## 新的动画系统

SwiftUI 5.0 引入了全新的动画系统，提供了更加灵活和强大的动画控制能力。

### 关键帧动画

新的关键帧动画 API 让我们能够创建复杂的多阶段动画：

\`\`\`swift
struct KeyframeAnimationView: View {
    @State private var isAnimating = false
    
    var body: some View {
        Circle()
            .fill(.blue)
            .frame(width: 100, height: 100)
            .keyframeAnimator(
                initialValue: AnimationValues(),
                repeating: isAnimating
            ) { content, value in
                content
                    .scaleEffect(value.scale)
                    .rotationEffect(value.rotation)
                    .offset(value.offset)
            } keyframes: { _ in
                KeyframeTrack(\.scale) {
                    SpringKeyframe(1.2, duration: 0.5)
                    SpringKeyframe(1.0, duration: 0.5)
                }
                KeyframeTrack(\.rotation) {
                    LinearKeyframe(.degrees(0), duration: 0.0)
                    LinearKeyframe(.degrees(360), duration: 1.0)
                }
                KeyframeTrack(\.offset) {
                    SpringKeyframe(CGSize(width: 100, height: 0), duration: 0.5)
                    SpringKeyframe(CGSize(width: 0, height: 0), duration: 0.5)
                }
            }
    }
}

struct AnimationValues {
    var scale = 1.0
    var rotation = Angle.zero
    var offset = CGSize.zero
}
\`\`\`

### 阶段动画

阶段动画让我们能够根据不同的状态创建不同的动画效果：

\`\`\`swift
enum LoadingPhase: CaseIterable {
    case initial, loading, success, error
}

struct PhaseAnimationView: View {
    @State private var phase: LoadingPhase = .initial
    
    var body: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(colorForPhase(phase))
            .frame(width: 200, height: 60)
            .phaseAnimator(LoadingPhase.allCases) { content, phase in
                content
                    .scaleEffect(scaleForPhase(phase))
                    .opacity(opacityForPhase(phase))
            } animation: { phase in
                switch phase {
                case .initial: .easeInOut(duration: 0.3)
                case .loading: .easeInOut(duration: 0.8).repeatForever()
                case .success: .spring(duration: 0.5)
                case .error: .easeInOut(duration: 0.3)
                }
            }
    }
}
\`\`\`

## 改进的数据绑定

SwiftUI 5.0 对数据绑定系统进行了重大改进，引入了新的 @Observable 宏和更强大的数据流管理。

### @Observable 宏

新的 @Observable 宏简化了可观察对象的创建：

\`\`\`swift
@Observable
class UserStore {
    var name: String = ""
    var email: String = ""
    var isLoggedIn: Bool = false
    
    func login() {
        // 登录逻辑
        isLoggedIn = true
    }
    
    func logout() {
        // 登出逻辑
        isLoggedIn = false
        name = ""
        email = ""
    }
}

struct UserProfileView: View {
    @State private var userStore = UserStore()
    
    var body: some View {
        VStack {
            if userStore.isLoggedIn {
                Text("欢迎, \\(userStore.name)")
                Button("登出") {
                    userStore.logout()
                }
            } else {
                TextField("用户名", text: $userStore.name)
                TextField("邮箱", text: $userStore.email)
                Button("登录") {
                    userStore.login()
                }
            }
        }
    }
}
\`\`\`

## 增强的导航功能

SwiftUI 5.0 带来了全新的导航系统，提供了更加灵活和强大的导航控制。

### NavigationStack 的改进

新的 NavigationStack 提供了更好的导航控制：

\`\`\`swift
struct NavigationExample: View {
    @State private var path = NavigationPath()
    
    var body: some View {
        NavigationStack(path: $path) {
            List {
                NavigationLink("用户列表", value: "users")
                NavigationLink("设置", value: "settings")
                NavigationLink("关于", value: "about")
            }
            .navigationDestination(for: String.self) { value in
                switch value {
                case "users":
                    UserListView()
                case "settings":
                    SettingsView()
                case "about":
                    AboutView()
                default:
                    Text("未知页面")
                }
            }
        }
    }
}
\`\`\`

## 性能优化

SwiftUI 5.0 在性能方面也有显著提升，特别是在大型列表和复杂视图的渲染方面。

### 懒加载改进

新的懒加载机制提供了更好的内存管理：

\`\`\`swift
struct OptimizedListView: View {
    let items: [Item]
    
    var body: some View {
        LazyVStack(spacing: 8) {
            ForEach(items) { item in
                ItemRowView(item: item)
                    .id(item.id)
            }
        }
        .scrollTargetLayout()
    }
}
\`\`\`

## 总结

SwiftUI 5.0 的这些新特性为 iOS 开发带来了前所未有的可能性。通过合理使用这些新功能，我们可以创建更加流畅、美观和高性能的应用程序。

在下一篇文章中，我们将深入探讨如何在实际项目中迁移到 SwiftUI 5.0，以及一些最佳实践和注意事项。

---

*本文是 SwiftUI 5.0 系列文章的第一篇，后续我们将继续分享更多实用的开发技巧和最佳实践。*
      `,
      date: '2024-12-20',
      tags: ['SwiftUI', 'iOS', 'Animation', 'Navigation'],
      category: 'ios',
      readTime: '12 分钟阅读',
      author: '李明',
      featured: true
    },
    en: {
      slug: 'swiftui-5-new-features',
      title: 'Complete Guide to SwiftUI 5.0 New Features',
      excerpt: 'Deep dive into SwiftUI 5.0 revolutionary new features, including the new animation system, improved data binding, enhanced navigation, and how to apply these features in real projects.',
      content: `
# Complete Guide to SwiftUI 5.0 New Features

SwiftUI 5.0 brings many exciting new features that will revolutionize how we build iOS applications. In this article, we'll dive deep into these new features and learn how to apply them in real projects.

## New Animation System

SwiftUI 5.0 introduces a brand new animation system that provides more flexible and powerful animation control capabilities.

### Keyframe Animations

The new keyframe animation API allows us to create complex multi-stage animations:

\`\`\`swift
struct KeyframeAnimationView: View {
    @State private var isAnimating = false
    
    var body: some View {
        Circle()
            .fill(.blue)
            .frame(width: 100, height: 100)
            .keyframeAnimator(
                initialValue: AnimationValues(),
                repeating: isAnimating
            ) { content, value in
                content
                    .scaleEffect(value.scale)
                    .rotationEffect(value.rotation)
                    .offset(value.offset)
            } keyframes: { _ in
                KeyframeTrack(\.scale) {
                    SpringKeyframe(1.2, duration: 0.5)
                    SpringKeyframe(1.0, duration: 0.5)
                }
                KeyframeTrack(\.rotation) {
                    LinearKeyframe(.degrees(0), duration: 0.0)
                    LinearKeyframe(.degrees(360), duration: 1.0)
                }
                KeyframeTrack(\.offset) {
                    SpringKeyframe(CGSize(width: 100, height: 0), duration: 0.5)
                    SpringKeyframe(CGSize(width: 0, height: 0), duration: 0.5)
                }
            }
    }
}

struct AnimationValues {
    var scale = 1.0
    var rotation = Angle.zero
    var offset = CGSize.zero
}
\`\`\`

### Phase Animations

Phase animations allow us to create different animation effects based on different states:

\`\`\`swift
enum LoadingPhase: CaseIterable {
    case initial, loading, success, error
}

struct PhaseAnimationView: View {
    @State private var phase: LoadingPhase = .initial
    
    var body: some View {
        RoundedRectangle(cornerRadius: 12)
            .fill(colorForPhase(phase))
            .frame(width: 200, height: 60)
            .phaseAnimator(LoadingPhase.allCases) { content, phase in
                content
                    .scaleEffect(scaleForPhase(phase))
                    .opacity(opacityForPhase(phase))
            } animation: { phase in
                switch phase {
                case .initial: .easeInOut(duration: 0.3)
                case .loading: .easeInOut(duration: 0.8).repeatForever()
                case .success: .spring(duration: 0.5)
                case .error: .easeInOut(duration: 0.3)
                }
            }
    }
}
\`\`\`

## Improved Data Binding

SwiftUI 5.0 has made significant improvements to the data binding system, introducing the new @Observable macro and more powerful data flow management.

### @Observable Macro

The new @Observable macro simplifies the creation of observable objects:

\`\`\`swift
@Observable
class UserStore {
    var name: String = ""
    var email: String = ""
    var isLoggedIn: Bool = false
    
    func login() {
        // Login logic
        isLoggedIn = true
    }
    
    func logout() {
        // Logout logic
        isLoggedIn = false
        name = ""
        email = ""
    }
}

struct UserProfileView: View {
    @State private var userStore = UserStore()
    
    var body: some View {
        VStack {
            if userStore.isLoggedIn {
                Text("Welcome, \\(userStore.name)")
                Button("Logout") {
                    userStore.logout()
                }
            } else {
                TextField("Username", text: $userStore.name)
                TextField("Email", text: $userStore.email)
                Button("Login") {
                    userStore.login()
                }
            }
        }
    }
}
\`\`\`

## Enhanced Navigation

SwiftUI 5.0 brings a completely new navigation system that provides more flexible and powerful navigation control.

### NavigationStack Improvements

The new NavigationStack provides better navigation control:

\`\`\`swift
struct NavigationExample: View {
    @State private var path = NavigationPath()
    
    var body: some View {
        NavigationStack(path: $path) {
            List {
                NavigationLink("User List", value: "users")
                NavigationLink("Settings", value: "settings")
                NavigationLink("About", value: "about")
            }
            .navigationDestination(for: String.self) { value in
                switch value {
                case "users":
                    UserListView()
                case "settings":
                    SettingsView()
                case "about":
                    AboutView()
                default:
                    Text("Unknown Page")
                }
            }
        }
    }
}
\`\`\`

## Performance Optimizations

SwiftUI 5.0 also has significant performance improvements, especially in rendering large lists and complex views.

### Lazy Loading Improvements

The new lazy loading mechanism provides better memory management:

\`\`\`swift
struct OptimizedListView: View {
    let items: [Item]
    
    var body: some View {
        LazyVStack(spacing: 8) {
            ForEach(items) { item in
                ItemRowView(item: item)
                    .id(item.id)
            }
        }
        .scrollTargetLayout()
    }
}
\`\`\`

## Conclusion

These new features in SwiftUI 5.0 bring unprecedented possibilities to iOS development. By properly using these new features, we can create more fluid, beautiful, and high-performance applications.

In the next article, we'll dive deep into how to migrate to SwiftUI 5.0 in real projects, along with some best practices and considerations.

---

*This article is the first in the SwiftUI 5.0 series. We'll continue to share more practical development tips and best practices.*
      `,
      date: '2024-12-20',
      tags: ['SwiftUI', 'iOS', 'Animation', 'Navigation'],
      category: 'ios',
      readTime: '12 min read',
      author: 'Ming Li',
      featured: true
    }
  },
  {
    zh: {
      slug: 'nextjs-15-multilingual-guide',
      title: '用 Next.js 15 构建多语言网站完整指南',
      excerpt: '深入探讨如何使用 Next.js 15 的 App Router 构建支持多语言的现代网站，包括路由配置、SEO 优化、性能提升等最佳实践。',
      content: `
# 用 Next.js 15 构建多语言网站完整指南

在全球化的今天，构建支持多语言的网站已经成为现代 Web 开发的必备技能。Next.js 15 的 App Router 为多语言网站开发提供了强大而灵活的解决方案。

## 项目初始化

首先，让我们创建一个新的 Next.js 15 项目：

\`\`\`bash
npx create-next-app@latest my-multilingual-site
cd my-multilingual-site
npm install next-intl
\`\`\`

## 配置国际化

### 1. 创建语言配置文件

创建 \`src/lib/i18n.ts\`：

\`\`\`typescript
export const locales = ['zh', 'en', 'fr', 'de'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'zh';

export const localeNames: Record<Locale, string> = {
  zh: '中文',
  en: 'English',
  fr: 'Français',
  de: 'Deutsch'
};
\`\`\`

### 2. 配置中间件

创建 \`middleware.ts\`：

\`\`\`typescript
import { createMiddleware } from 'next-intl/middleware';
import { locales, defaultLocale } from './src/lib/i18n';

export default createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always'
});

export const config = {
  matcher: [
    '/((?!api|_next|_vercel|.*\\..*).*)',
    '/([\\w-]+)?/users/(.+)'
  ]
};
\`\`\`

## 路由结构设计

使用 App Router 的动态路由功能：

\`\`\`
src/app/
├── [locale]/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── about/
│   │   └── page.tsx
│   ├── blog/
│   │   ├── page.tsx
│   │   └── [slug]/
│   │       └── page.tsx
│   └── contact/
│       └── page.tsx
├── globals.css
└── layout.tsx
\`\`\`

## 翻译文件管理

### 创建翻译文件

\`messages/zh.json\`：
\`\`\`json
{
  "navigation": {
    "home": "首页",
    "about": "关于我们",
    "blog": "博客",
    "contact": "联系我们"
  },
  "home": {
    "title": "欢迎来到我们的网站",
    "description": "这是一个多语言网站示例"
  }
}
\`\`\`

\`messages/en.json\`：
\`\`\`json
{
  "navigation": {
    "home": "Home",
    "about": "About",
    "blog": "Blog",
    "contact": "Contact"
  },
  "home": {
    "title": "Welcome to Our Website",
    "description": "This is a multilingual website example"
  }
}
\`\`\`

## 组件国际化

### 创建导航组件

\`\`\`tsx
'use client';

import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import Link from 'next/link';

export default function Navigation() {
  const t = useTranslations('navigation');
  const params = useParams();
  const locale = params.locale as string;

  const navItems = [
    { key: 'home', href: \`/\${locale}\` },
    { key: 'about', href: \`/\${locale}/about\` },
    { key: 'blog', href: \`/\${locale}/blog\` },
    { key: 'contact', href: \`/\${locale}/contact\` }
  ];

  return (
    <nav className="flex space-x-6">
      {navItems.map(item => (
        <Link
          key={item.key}
          href={item.href}
          className="hover:text-blue-600 transition-colors"
        >
          {t(item.key)}
        </Link>
      ))}
    </nav>
  );
}
\`\`\`

## SEO 优化

### 多语言 SEO 配置

\`\`\`tsx
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

interface Props {
  params: { locale: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'metadata' });

  return {
    title: t('title'),
    description: t('description'),
    alternates: {
      canonical: \`https://example.com/\${params.locale}\`,
      languages: {
        'zh': 'https://example.com/zh',
        'en': 'https://example.com/en',
        'fr': 'https://example.com/fr',
        'de': 'https://example.com/de'
      }
    },
    openGraph: {
      title: t('title'),
      description: t('description'),
      locale: params.locale,
      alternateLocale: ['zh', 'en', 'fr', 'de'].filter(l => l !== params.locale)
    }
  };
}
\`\`\`

## 性能优化

### 1. 动态导入翻译文件

\`\`\`typescript
async function getMessages(locale: string) {
  try {
    return (await import(\`../messages/\${locale}.json\`)).default;
  } catch (error) {
    return (await import('../messages/zh.json')).default;
  }
}
\`\`\`

### 2. 静态生成优化

\`\`\`tsx
export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
\`\`\`

## 语言切换器

\`\`\`tsx
'use client';

import { useParams, usePathname } from 'next/navigation';
import Link from 'next/link';
import { locales, localeNames } from '@/lib/i18n';

export default function LanguageSwitcher() {
  const params = useParams();
  const pathname = usePathname();
  const currentLocale = params.locale as string;

  const getLocalizedPath = (locale: string) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="relative">
      <select
        value={currentLocale}
        onChange={(e) => {
          const newPath = getLocalizedPath(e.target.value);
          window.location.href = newPath;
        }}
        className="bg-white border border-gray-300 rounded px-3 py-1"
      >
        {locales.map(locale => (
          <option key={locale} value={locale}>
            {localeNames[locale]}
          </option>
        ))}
      </select>
    </div>
  );
}
\`\`\`

## 最佳实践

### 1. 翻译键命名规范

- 使用嵌套结构组织翻译键
- 保持键名简洁且具有描述性
- 使用一致的命名约定

### 2. 内容管理

- 考虑使用 CMS 管理多语言内容
- 实现翻译工作流程
- 定期审查和更新翻译

### 3. 测试策略

\`\`\`typescript
// 测试多语言路由
describe('Multilingual Routing', () => {
  test('should redirect to default locale', () => {
    // 测试逻辑
  });

  test('should preserve locale in navigation', () => {
    // 测试逻辑
  });
});
\`\`\`

## 总结

Next.js 15 的 App Router 为构建多语言网站提供了强大的工具和灵活的架构。通过合理的配置和优化，我们可以创建出性能优异、SEO 友好的多语言网站。

关键要点：
- 使用中间件处理语言检测和重定向
- 合理组织翻译文件和路由结构
- 重视 SEO 优化和性能提升
- 建立完善的测试和维护流程

---

*在下一篇文章中，我们将探讨如何在多语言网站中实现复杂的内容管理和动态翻译功能。*
      `,
      date: '2024-12-15',
      tags: ['Next.js', 'i18n', 'SEO', 'App Router'],
      category: 'frontend',
      readTime: '8 分钟阅读',
      author: '张伟',
      featured: false
    },
    en: {
      slug: 'nextjs-15-multilingual-guide',
      title: 'Complete Guide to Building Multilingual Sites with Next.js 15',
      excerpt: 'An in-depth exploration of building modern multilingual websites using Next.js 15 App Router, including routing configuration, SEO optimization, and performance best practices.',
      content: `
# Complete Guide to Building Multilingual Sites with Next.js 15

In today's globalized world, building multilingual websites has become an essential skill for modern web development. Next.js 15's App Router provides powerful and flexible solutions for multilingual website development.

## Project Initialization

First, let's create a new Next.js 15 project:

\`\`\`bash
npx create-next-app@latest my-multilingual-site
cd my-multilingual-site
npm install next-intl
\`\`\`

## Internationalization Configuration

### 1. Create Language Configuration File

Create \`src/lib/i18n.ts\`:

\`\`\`typescript
export const locales = ['zh', 'en', 'fr', 'de'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'en';

export const localeNames: Record<Locale, string> = {
  zh: '中文',
  en: 'English',
  fr: 'Français',
  de: 'Deutsch'
};
\`\`\`

### 2. Configure Middleware

Create \`middleware.ts\`:

\`\`\`typescript
import { createMiddleware } from 'next-intl/middleware';
import { locales, defaultLocale } from './src/lib/i18n';

export default createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'always'
});

export const config = {
  matcher: [
    '/((?!api|_next|_vercel|.*\\..*).*)',
    '/([\\w-]+)?/users/(.+)'
  ]
};
\`\`\`

## Route Structure Design

Using App Router's dynamic routing features:

\`\`\`
src/app/
├── [locale]/
│   ├── layout.tsx
│   ├── page.tsx
│   ├── about/
│   │   └── page.tsx
│   ├── blog/
│   │   ├── page.tsx
│   │   └── [slug]/
│   │       └── page.tsx
│   └── contact/
│       └── page.tsx
├── globals.css
└── layout.tsx
\`\`\`

## Translation File Management

### Create Translation Files

\`messages/zh.json\`:
\`\`\`json
{
  "navigation": {
    "home": "首页",
    "about": "关于我们",
    "blog": "博客",
    "contact": "联系我们"
  },
  "home": {
    "title": "欢迎来到我们的网站",
    "description": "这是一个多语言网站示例"
  }
}
\`\`\`

\`messages/en.json\`:
\`\`\`json
{
  "navigation": {
    "home": "Home",
    "about": "About",
    "blog": "Blog",
    "contact": "Contact"
  },
  "home": {
    "title": "Welcome to Our Website",
    "description": "This is a multilingual website example"
  }
}
\`\`\`

## Component Internationalization

### Create Navigation Component

\`\`\`tsx
'use client';

import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import Link from 'next/link';

export default function Navigation() {
  const t = useTranslations('navigation');
  const params = useParams();
  const locale = params.locale as string;

  const navItems = [
    { key: 'home', href: \`/\${locale}\` },
    { key: 'about', href: \`/\${locale}/about\` },
    { key: 'blog', href: \`/\${locale}/blog\` },
    { key: 'contact', href: \`/\${locale}/contact\` }
  ];

  return (
    <nav className="flex space-x-6">
      {navItems.map(item => (
        <Link
          key={item.key}
          href={item.href}
          className="hover:text-blue-600 transition-colors"
        >
          {t(item.key)}
        </Link>
      ))}
    </nav>
  );
}
\`\`\`

## SEO Optimization

### Multilingual SEO Configuration

\`\`\`tsx
import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

interface Props {
  params: { locale: string };
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'metadata' });

  return {
    title: t('title'),
    description: t('description'),
    alternates: {
      canonical: \`https://example.com/\${params.locale}\`,
      languages: {
        'zh': 'https://example.com/zh',
        'en': 'https://example.com/en',
        'fr': 'https://example.com/fr',
        'de': 'https://example.com/de'
      }
    },
    openGraph: {
      title: t('title'),
      description: t('description'),
      locale: params.locale,
      alternateLocale: ['zh', 'en', 'fr', 'de'].filter(l => l !== params.locale)
    }
  };
}
\`\`\`

## Performance Optimization

### 1. Dynamic Import of Translation Files

\`\`\`typescript
async function getMessages(locale: string) {
  try {
    return (await import(\`../messages/\${locale}.json\`)).default;
  } catch (error) {
    return (await import('../messages/en.json')).default;
  }
}
\`\`\`

### 2. Static Generation Optimization

\`\`\`tsx
export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
\`\`\`

## Language Switcher

\`\`\`tsx
'use client';

import { useParams, usePathname } from 'next/navigation';
import Link from 'next/link';
import { locales, localeNames } from '@/lib/i18n';

export default function LanguageSwitcher() {
  const params = useParams();
  const pathname = usePathname();
  const currentLocale = params.locale as string;

  const getLocalizedPath = (locale: string) => {
    const segments = pathname.split('/');
    segments[1] = locale;
    return segments.join('/');
  };

  return (
    <div className="relative">
      <select
        value={currentLocale}
        onChange={(e) => {
          const newPath = getLocalizedPath(e.target.value);
          window.location.href = newPath;
        }}
        className="bg-white border border-gray-300 rounded px-3 py-1"
      >
        {locales.map(locale => (
          <option key={locale} value={locale}>
            {localeNames[locale]}
          </option>
        ))}
      </select>
    </div>
  );
}
\`\`\`

## Best Practices

### 1. Translation Key Naming Conventions

- Use nested structures to organize translation keys
- Keep key names concise and descriptive
- Use consistent naming conventions

### 2. Content Management

- Consider using CMS for multilingual content management
- Implement translation workflows
- Regularly review and update translations

### 3. Testing Strategy

\`\`\`typescript
// Test multilingual routing
describe('Multilingual Routing', () => {
  test('should redirect to default locale', () => {
    // Test logic
  });

  test('should preserve locale in navigation', () => {
    // Test logic
  });
});
\`\`\`

## Conclusion

Next.js 15's App Router provides powerful tools and flexible architecture for building multilingual websites. With proper configuration and optimization, we can create high-performance, SEO-friendly multilingual websites.

Key takeaways:
- Use middleware for language detection and redirection
- Properly organize translation files and route structure
- Focus on SEO optimization and performance improvement
- Establish comprehensive testing and maintenance processes

---

*In the next article, we'll explore how to implement complex content management and dynamic translation features in multilingual websites.*
      `,
      date: '2024-12-15',
      tags: ['Next.js', 'i18n', 'SEO', 'App Router'],
      category: 'frontend',
      readTime: '8 min read',
      author: 'David Zhang',
      featured: false
    }
  }
];

// 根据语言和 slug 获取文章
export function getBlogPost(slug: string, locale: Locale): BlogPost | null {
  const post = blogPosts.find(p => p[locale].slug === slug);
  return post ? post[locale] : null;
}

// 获取所有文章（指定语言）
export function getAllBlogPosts(locale: Locale): BlogPost[] {
  return blogPosts.map(p => p[locale]);
}

// 获取相关文章
export function getRelatedPosts(currentSlug: string, locale: Locale, limit: number = 3): BlogPost[] {
  const currentPost = getBlogPost(currentSlug, locale);
  if (!currentPost) return [];
  
  const allPosts = getAllBlogPosts(locale);
  const relatedPosts = allPosts
    .filter(post => post.slug !== currentSlug)
    .filter(post => 
      post.category === currentPost.category || 
      post.tags.some(tag => currentPost.tags.includes(tag))
    )
    .slice(0, limit);
    
  // 如果相关文章不够，用其他文章补充
  if (relatedPosts.length < limit) {
    const otherPosts = allPosts
      .filter(post => post.slug !== currentSlug)
      .filter(post => !relatedPosts.includes(post))
      .slice(0, limit - relatedPosts.length);
    relatedPosts.push(...otherPosts);
  }
  
  return relatedPosts;
}
