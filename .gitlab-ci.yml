# GitLab CI/CD 配置文件 - 三娃软件网站自动部署
# 服务器: ************
# SSH Key: gitlab_ssh_id 触发构建

# 定义阶段
stages:
  - deploy
  - health-check

# 全局变量
variables:
  NODE_VERSION: "20"
  NPM_CONFIG_CACHE: "$CI_PROJECT_DIR/.npm"

  # 部署配置
  DEPLOY_HOST: "************"
  DEPLOY_USER: "root"
  DEPLOY_PATH: "/root/gitlab/sanva_website"
  PRODUCTION_PORT: "55300"
  STAGING_PORT: "55301"

# 缓存配置 - 优化构建速度 
cache:
  key:
    files:
      - yarn.lock
  paths:
    - node_modules/
    - .yarn-cache/
  policy: pull-push





# 部署到生产环境（远程构建部署）
deploy:production:
  stage: deploy
  tags:
    - Eva
  variables:
    GIT_STRATEGY: none  # Eva节点不需要拉取代码
  before_script:
    - echo "🔧 配置 SSH 连接..."
    - echo "检查必要工具..."
    - which ssh || echo "SSH 已安装"

    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
    # 测试 SSH 连接
    - ssh -o ConnectTimeout=10 $DEPLOY_USER@$DEPLOY_HOST "echo '✅ SSH 连接成功'"
  script:
    - |
      echo "🚀 在远程服务器上执行完整的拉取、构建和部署流程..."
      ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
        set -e

        # 颜色定义
        RED='\033[0;31m'
        GREEN='\033[0;32m'
        YELLOW='\033[1;33m'
        BLUE='\033[0;34m'
        NC='\033[0m'

        log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
        log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
        log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
        log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }

        # 进入部署目录
        log_info "进入部署目录..."
        cd /root/gitlab/sanva_website

        # 检查当前目录
        log_info "当前目录: $(pwd)"
        log_info "检查 git 状态..."
        git status || log_error "不是 git 仓库"

        # 拉取最新代码
        log_info "拉取最新代码..."
        git fetch origin
        git reset --hard origin/main
        git clean -fd

        log_success "代码更新完成"
        git log --oneline -3

        # 设置环境变量和PATH
        log_info "设置环境变量..."
        export NVM_DIR="/root/.nvm"
        export PATH="/root/.nvm/versions/node/v20.16.0/bin:/usr/local/bin:/usr/bin:/bin:/root/.local/bin:$PATH"
        export NODE_ENV=production
        export NEXT_TELEMETRY_DISABLED=1

        # 检查工具可用性
        log_info "检查工具可用性..."
        echo "当前 PATH: $PATH"
        which node 2>/dev/null || echo "Node.js 未在 PATH 中找到"
        which yarn 2>/dev/null || echo "Yarn 未在 PATH 中找到"
        which npm 2>/dev/null || echo "NPM 未在 PATH 中找到"
        which next 2>/dev/null || echo "Next.js CLI 未在 PATH 中找到"
        which pm2 2>/dev/null || echo "PM2 未在 PATH 中找到"

        # 检查和安装全局依赖
        log_info "检查和安装全局依赖..."
        if ! which next >/dev/null 2>&1; then
          log_warning "Next.js CLI 未安装，正在安装..."
          npm install -g next
        fi

        if ! which pm2 >/dev/null 2>&1; then
          log_warning "PM2 未安装，正在安装..."
          npm install -g pm2
        fi

        # 验证关键工具
        log_info "验证关键工具..."
        node --version
        yarn --version || npm --version
        next --version
        pm2 --version

        # 清理和重新安装项目依赖
        log_info "清理旧依赖..."
        rm -rf node_modules .next

        log_info "安装项目依赖（包括开发依赖）..."
        if which yarn >/dev/null 2>&1; then
          log_info "使用 yarn 重新安装所有依赖..."
          yarn install
        else
          log_info "使用 npm 安装所有依赖..."
          npm install
        fi

        log_success "依赖安装完成"

        # 快速构建和部署
        log_info "执行快速构建和部署..."
        if which yarn >/dev/null 2>&1; then
          log_info "使用 yarn 进行部署..."
          yarn pm2:start
        elif which npm >/dev/null 2>&1; then
          log_info "使用 npm 进行部署..."
          npm run pm2:start
        else
          log_error "yarn 和 npm 都不可用！"
          exit 1
        fi

        log_success "构建和部署完成！"

        # 清理旧备份（保留最近3个）
        log_info "清理旧备份..."
        ls -t .next.backup_* 2>/dev/null | tail -n +4 | xargs rm -rf 2>/dev/null || true

        log_success "部署完成！"
        echo ""
        echo "📊 PM2 状态："
        pm2 list
        echo ""
        echo "🌐 服务地址: http://************:55300"
        echo "📝 查看日志: pm2 logs sanva-website"
      EOF
  environment:
    name: production
    url: http://$DEPLOY_HOST:$PRODUCTION_PORT
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $CI_COMMIT_MESSAGE =~ /^release:/
      when: on_success
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual

  timeout: 10m

# 健康检查
health-check:production:
  stage: health-check
  tags:
    - Eva

  variables:
    GIT_STRATEGY: none
  before_script:
    - echo "🏥 准备健康检查..."
    - which curl || echo "curl 已安装"
  script:
    - echo "🏥 执行生产环境健康检查..."
    - sleep 10  # 等待服务完全启动
    - |
      # 检查服务是否响应
      for i in {1..5}; do
        echo "尝试连接服务 (第 $i 次)..."
        if curl -f --connect-timeout 10 --max-time 30 http://$DEPLOY_HOST:$PRODUCTION_PORT > /dev/null 2>&1; then
          echo "✅ 服务健康检查通过！"
          echo "🌐 服务地址: http://$DEPLOY_HOST:$PRODUCTION_PORT"
          exit 0
        fi
        echo "⏳ 等待服务启动..."
        sleep 10
      done
      echo "❌ 健康检查失败，服务可能未正常启动"
      exit 1
  rules:
    - if: $CI_COMMIT_BRANCH == "main" && $CI_COMMIT_MESSAGE =~ /^release:/
      when: on_success
    - if: $CI_COMMIT_BRANCH == "main"
      when: never

  timeout: 5m

# 回滚部署（手动触发）
rollback:production:
  stage: deploy
  tags:
    - Eva

  variables:
    GIT_STRATEGY: none
  before_script:
    - echo "🔄 准备回滚操作..."
    - which ssh || echo "SSH 已安装"
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H $DEPLOY_HOST >> ~/.ssh/known_hosts
  script:
    - |
      echo "🔄 执行生产环境回滚..."
      ssh $DEPLOY_USER@$DEPLOY_HOST << 'EOF'
        set -e
        cd $DEPLOY_PATH

        if [ -d ".next.backup" ]; then
          echo "📦 恢复备份版本..."
          rm -rf .next
          mv .next.backup .next

          echo "🚀 重载 PM2 服务..."
          pm2 reload ecosystem.config.js --env production

          echo "✅ 回滚完成！"
          pm2 list
        else
          echo "❌ 未找到备份版本，无法回滚"
          exit 1
        fi
      EOF
  environment:
    name: production
    url: http://$DEPLOY_HOST:$PRODUCTION_PORT
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
  timeout: 5m
